# Linux Do API端点修复

## 问题描述

在初始实现中，用户信息端点使用了错误的URL：
- ❌ 错误：`https://connect.linux.do/oauth2/userinfo`
- ✅ 正确：`https://connect.linux.do/api/user`

这导致获取用户信息时出现403 Forbidden错误。

## 修复内容

### 1. 更新API端点配置

在 `app.py` 中修复了用户信息端点：

```python
# 修复前
LINUX_DO_USER_INFO_URL = 'https://connect.linux.do/oauth2/userinfo'

# 修复后
LINUX_DO_USER_INFO_URL = 'https://connect.linux.do/api/user'
```

### 2. 更新文档

同时更新了以下文档文件：
- `LINUX_DO_OAUTH_README.md`
- `DEPLOYMENT_CHECKLIST.md`

## Linux Do OAuth2 正确的端点

根据Linux Do官方文档，正确的端点应该是：

- **授权端点**: `https://connect.linux.do/oauth2/authorize`
- **Token端点**: `https://connect.linux.do/oauth2/token`
- **用户信息端点**: `https://connect.linux.do/api/user`

## 测试结果

API端点测试结果：
- ✅ 授权端点可访问
- ✅ Token端点可访问
- ✅ 用户信息端点可访问（返回401，符合预期，需要Bearer token）

## 部署说明

修复后的代码已经可以正常工作。用户现在可以：

1. 点击"Linux Do 快捷登录"按钮
2. 跳转到Linux Do授权页面
3. 完成授权后正确获取用户信息
4. 自动注册或登录到系统

## 注意事项

- 确保服务器运行在HTTPS环境下
- 确保回调地址 `https://sd.exacg.cc/linux` 与服务器域名匹配
- 监控日志以确保OAuth2流程正常工作

## 验证方法

可以通过以下方式验证修复是否成功：

1. 查看应用日志，确认不再出现403错误
2. 完整测试OAuth2登录流程
3. 确认用户信息能正确获取和保存
